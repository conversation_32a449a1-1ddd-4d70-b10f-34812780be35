#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育部第四轮学科评估数据爬虫
爬取所有学科分类的评估数据
"""

import requests
import json
import time
import csv
import os
from typing import List, Dict, Any
import logging
from urllib.parse import urlencode

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SubjectEvaluationCrawler:
    def __init__(self):
        self.base_url = "https://www.cingta.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.cingta.com/opendata/detail/191'
        })
        
        # 学科分类映射
        self.subject_categories = {
            '哲学': 'philosophy',
            '经济学': 'economics', 
            '法学': 'law',
            '教育学': 'education',
            '文学': 'literature',
            '历史学': 'history',
            '理学': 'science',
            '工学': 'engineering',
            '农学': 'agriculture',
            '医学': 'medicine',
            '管理学': 'management',
            '艺术学': 'arts',
            '交叉学科': 'interdisciplinary'
        }
        
        self.all_data = []
        
    def get_api_data(self, category: str = None, page: int = 1, page_size: int = 50) -> Dict[str, Any]:
        """
        获取API数据
        """
        try:
            # 构建API请求URL - 需要分析实际的API接口
            api_url = f"{self.base_url}/api/opendata/subject-evaluation"
            
            params = {
                'page': page,
                'pageSize': page_size,
                'datasetId': 191
            }
            
            if category:
                params['category'] = category
                
            logger.info(f"请求API: {api_url}, 参数: {params}")
            
            response = self.session.get(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"API请求失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"API请求异常: {str(e)}")
            return None
    
    def parse_page_data(self, html_content: str, category: str) -> List[Dict[str, Any]]:
        """
        解析页面HTML数据
        """
        from bs4 import BeautifulSoup
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找数据表格
            table_rows = soup.find_all('tr')
            data_list = []
            
            for row in table_rows[1:]:  # 跳过表头
                cells = row.find_all('td')
                if len(cells) >= 5:
                    data_item = {
                        '序号': cells[0].get_text(strip=True),
                        '单位名称': cells[1].get_text(strip=True),
                        '一级学科': cells[2].get_text(strip=True),
                        '评估结果': cells[3].get_text(strip=True),
                        '排名占比': cells[4].get_text(strip=True),
                        '学科分类': category
                    }
                    data_list.append(data_item)
                    
            return data_list
            
        except Exception as e:
            logger.error(f"解析页面数据异常: {str(e)}")
            return []
    
    def get_category_data(self, category: str) -> List[Dict[str, Any]]:
        """
        获取特定分类的所有数据
        """
        logger.info(f"开始爬取分类: {category}")
        category_data = []
        page = 1
        
        while True:
            try:
                # 构建带分类参数的URL
                url = f"{self.base_url}/opendata/detail/191"
                params = {
                    'name': '教育部第四轮学科评估数据',
                    'category': category,
                    'page': page
                }
                
                logger.info(f"请求页面: {url}, 页码: {page}")
                
                response = self.session.get(url, params=params, timeout=30)
                
                if response.status_code == 200:
                    page_data = self.parse_page_data(response.text, category)
                    
                    if not page_data:
                        logger.info(f"分类 {category} 第 {page} 页无数据，结束爬取")
                        break
                        
                    category_data.extend(page_data)
                    logger.info(f"分类 {category} 第 {page} 页获取到 {len(page_data)} 条数据")
                    
                    page += 1
                    time.sleep(2)  # 避免请求过快
                    
                else:
                    logger.error(f"请求失败，状态码: {response.status_code}")
                    break
                    
            except Exception as e:
                logger.error(f"获取分类 {category} 数据异常: {str(e)}")
                break
                
        logger.info(f"分类 {category} 共获取到 {len(category_data)} 条数据")
        return category_data
    
    def crawl_all_categories(self):
        """
        爬取所有分类的数据
        """
        logger.info("开始爬取所有学科分类数据")
        
        for category_name in self.subject_categories.keys():
            try:
                category_data = self.get_category_data(category_name)
                self.all_data.extend(category_data)
                
                logger.info(f"已完成分类 {category_name}，累计数据: {len(self.all_data)} 条")
                
                # 分类间休息
                time.sleep(3)
                
            except Exception as e:
                logger.error(f"爬取分类 {category_name} 失败: {str(e)}")
                continue
                
        logger.info(f"所有分类爬取完成，总计: {len(self.all_data)} 条数据")
    
    def save_to_csv(self, filename: str = "subject_evaluation_data.csv"):
        """
        保存数据到CSV文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['序号', '单位名称', '一级学科', '评估结果', '排名占比', '学科分类']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for item in self.all_data:
                    writer.writerow(item)
                    
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")
    
    def save_to_json(self, filename: str = "subject_evaluation_data.json"):
        """
        保存数据到JSON文件
        """
        if not self.all_data:
            logger.warning("没有数据可保存")
            return
            
        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(self.all_data, jsonfile, ensure_ascii=False, indent=2)
                
            logger.info(f"数据已保存到 {filename}，共 {len(self.all_data)} 条记录")
            
        except Exception as e:
            logger.error(f"保存JSON文件失败: {str(e)}")

def main():
    """
    主函数
    """
    crawler = SubjectEvaluationCrawler()
    
    try:
        # 爬取所有分类数据
        crawler.crawl_all_categories()
        
        # 保存数据
        crawler.save_to_csv()
        crawler.save_to_json()
        
        print(f"爬取完成！共获取 {len(crawler.all_data)} 条数据")
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取")
        print("爬取被中断")
        
    except Exception as e:
        logger.error(f"爬取过程发生异常: {str(e)}")
        print(f"爬取失败: {str(e)}")

if __name__ == "__main__":
    main()
